import pandas as pd
import ast
from collections import Counter
import csv
from tqdm import tqdm

def load_data(file_path):
    """加载彩票数据"""
    df = pd.read_csv(file_path)
    # 解析numbers列
    df['numbers_list'] = df['numbers'].apply(lambda x: [int(num) for num in ast.literal_eval(x)])
    return df

def get_top_numbers(history_data):
    """获取历史数据中出现频次最高的数字"""
    all_numbers = []
    for numbers in history_data:
        all_numbers.extend(numbers)
    
    counter = Counter(all_numbers)
    
    if not counter:
        return [], False
    
    max_count = max(counter.values())
    top_numbers = [num for num, count in counter.items() if count == max_count]
    
    is_valid = len(top_numbers) == 2
    
    return sorted(top_numbers), is_valid

def check_prediction(prediction_numbers, actual_numbers):
    """检查预测是否命中"""
    return any(num in actual_numbers for num in prediction_numbers)

def backtest_single_window_size(df, window_size):
    """对单个window_size进行回测"""
    total_profit = 0

    bet_count = 0
    win_count = 0
    loss_count = 0

    # 记录所有投注结果，用于后续分析连续亏损
    bet_results = []

    # 从第window_size+1期开始预测
    for i in range(window_size, len(df)):
        history_data = df.iloc[i-window_size:i]['numbers_list'].tolist()
        current_numbers = df.iloc[i]['numbers_list']

        top_numbers, is_valid = get_top_numbers(history_data)

        if not is_valid:
            # 跳过这期，不进行投注
            continue

        # 进行预测
        bet_count += 1
        prediction_numbers = top_numbers
        hit = check_prediction(prediction_numbers, current_numbers)

        if hit:
            profit_change = 19
            total_profit += profit_change
            win_count += 1
            bet_results.append(True)  # 记录命中
        else:
            profit_change = -2
            total_profit += profit_change
            loss_count += 1
            bet_results.append(False)  # 记录未命中

    # 分析连续亏损
    loss_streaks = []
    current_streak = 0
    max_consecutive_losses = 0

    for hit in bet_results:
        if not hit:  # 亏损
            current_streak += 1
            max_consecutive_losses = max(max_consecutive_losses, current_streak)
        else:  # 盈利
            if current_streak > 0:
                loss_streaks.append(current_streak)
            current_streak = 0

    # 处理最后的连续亏损
    if current_streak > 0:
        loss_streaks.append(current_streak)

    # 计算当前连续失败次数（截至最新一期）
    current_consecutive_failures = 0
    if bet_results:
        # 从最后一次投注开始往前数连续失败次数
        for i in range(len(bet_results) - 1, -1, -1):
            if not bet_results[i]:  # 失败
                current_consecutive_failures += 1
            else:  # 成功，停止计数
                break

    # 计算命中率
    hit_rate = (win_count / bet_count * 100) if bet_count > 0 else 0

    # 统计连续亏损期数出现次数
    loss_streak_counts = dict(Counter(loss_streaks)) if loss_streaks else {}

    return {
        'window_size': window_size,
        'final_profit': total_profit,
        'bet_count': bet_count,
        'win_count': win_count,
        'loss_count': loss_count,
        'hit_rate': hit_rate,
        'max_consecutive_losses': max_consecutive_losses,
        'loss_streak_counts': loss_streak_counts,
        'current_consecutive_failures': current_consecutive_failures
    }

def analyze_all_window_sizes():
    """分析所有window_size从2到151"""
    print("正在加载数据...")
    df = load_data('happy8_data.csv')

    results = []

    print("开始分析不同的window_size...")
    for window_size in tqdm(range(2, 152), desc="分析进度"):
        result = backtest_single_window_size(df, window_size)
        results.append(result)

    return results, df

def get_next_prediction(df, window_size):
    """基于最新数据预测下一期号码"""
    if len(df) < window_size:
        return ""

    # 使用最新的window_size期数据
    history_data = df.tail(window_size)['numbers_list'].tolist()
    top_numbers, is_valid = get_top_numbers(history_data)

    if is_valid:
        return str(top_numbers)
    else:
        return ""

def export_to_csv(results, df):
    """导出结果到CSV文件"""
    # 准备CSV数据
    csv_data = []

    for result in results:
        # 基础数据
        row = {
            'window_size': result['window_size'],
            'final_profit': result['final_profit'],
            'bet_count': result['bet_count'],
            'win_count': result['win_count'],
            'loss_count': result['loss_count'],
            'hit_rate': round(result['hit_rate'], 2),
            'max_consecutive_losses': result['max_consecutive_losses'],
            'current_consecutive_failures': result['current_consecutive_failures']
        }

        # 添加连续亏损期数统计
        loss_streak_counts = result['loss_streak_counts']
        if loss_streak_counts:
            # 转换为字符串格式，便于CSV存储
            row['loss_streak_counts'] = str(loss_streak_counts)
        else:
            row['loss_streak_counts'] = '{}'

        # 添加下一期预测
        next_prediction = get_next_prediction(df, result['window_size'])
        row['next_prediction'] = next_prediction

        csv_data.append(row)

    # 写入CSV文件
    filename = 'window_size_analysis_results_2.csv'
    fieldnames = ['window_size', 'final_profit', 'bet_count', 'win_count', 'loss_count',
                  'hit_rate', 'max_consecutive_losses', 'current_consecutive_failures',
                  'loss_streak_counts', 'next_prediction']

    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(csv_data)

    print(f"\n结果已导出到: {filename}")
    return filename

def print_summary(results):
    """打印分析结果摘要"""
    print("\n" + "="*80)
    print("Window Size 分析结果摘要")
    print("="*80)
    
    # 找出最佳结果
    best_profit = max(results, key=lambda x: x['final_profit'])
    best_hit_rate = max(results, key=lambda x: x['hit_rate'])
    min_max_loss = min(results, key=lambda x: x['max_consecutive_losses'])
    
    print(f"最高收益: window_size={best_profit['window_size']}, 收益={best_profit['final_profit']}")
    print(f"最高命中率: window_size={best_hit_rate['window_size']}, 命中率={best_hit_rate['hit_rate']:.2f}%")
    print(f"最小最大连续亏损: window_size={min_max_loss['window_size']}, 最大连续亏损={min_max_loss['max_consecutive_losses']}期")
    
    # 统计正收益的window_size数量
    positive_profit_count = sum(1 for r in results if r['final_profit'] > 0)
    print(f"\n正收益的window_size数量: {positive_profit_count}/{len(results)}")
    
    # 显示前5个最佳收益
    print(f"\n收益排名前5:")
    sorted_by_profit = sorted(results, key=lambda x: x['final_profit'], reverse=True)
    for i, result in enumerate(sorted_by_profit[:5], 1):
        print(f"{i}. window_size={result['window_size']}: 收益={result['final_profit']}, "
              f"命中率={result['hit_rate']:.2f}%, 投注次数={result['bet_count']}")

if __name__ == "__main__":
    # 执行分析
    results, df = analyze_all_window_sizes()

    # 打印摘要
    print_summary(results)

    # 导出CSV（传入df用于预测）
    csv_filename = export_to_csv(results, df)

    print(f"\n分析完成！详细结果请查看: {csv_filename}")
